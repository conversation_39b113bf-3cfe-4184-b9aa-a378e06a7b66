"use client";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { Form } from "@/components/ui/form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import InputField from "@/components/InputField";
import SecondaryButton from "@/components/SecondaryButton";
import { useRouter } from "next/navigation";

const websiteNameSchema = z.object({
  websiteName: z.string().min(2, {
    message:
      "Website name must be at least 5 characters and no uppercase letters.",
  }),
});

type LoginFormData = z.infer<typeof websiteNameSchema>;

export default function LandingPage() {
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  const form = useForm<LoginFormData>({
    resolver: zodResolver(websiteNameSchema),
    defaultValues: {
      websiteName: "",
    },
  });

  async function onSubmit(values: LoginFormData) {
    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));
      router.replace(`/builder/${values.websiteName}`);
      console.log("Login data:", values);
      // Handle successful login here
    } catch (error) {
      console.error("Login error:", error);
    } finally {
      setIsLoading(false);
    }
  }
  return (
    <div className="flex justify-center items-center z-10 w-full h-full">
      <section className="pt-20 pb-16 px-4">
        <div className="max-w-7xl mx-auto ">
          <div className="mb-8 text-center">
            <h1 className="text-5xl md:text-7xl font-bold mb-6">
              <span className="bg-gradient-to-r from-white to-slate-300 bg-clip-text text-transparent">
                Create Your
              </span>
              <br />
              <span className="bg-gradient-to-r from-pink-500 to-purple-600 bg-clip-text text-transparent">
                Profile Card Just In Minutes
              </span>
            </h1>
            <p className="text-xl md:text-2xl text-slate-400 max-w-3xl mx-auto leading-relaxed">
              Create your profile card in minutes with MyTag and share it with
              the world. No sign-in or sign-up required , just enter your
              website name and you&apos;re good to go.
            </p>
          </div>
          <div className="w-full mx-auto">
            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="space-y-6 flex flex-col"
              >
                <InputField
                  control={form.control}
                  name="websiteName"
                  label="Your Website Name"
                  placeholder="ex : https://me.vercel.app/your-website-name"
                  type="text"
                />
                {/* Submit Button */}
                <SecondaryButton
                  type="submit"
                  disabled={isLoading}
                  text="Continue"
                  className="mt-2"
                />
              </form>
            </Form>
          </div>
        </div>
      </section>
    </div>
  );
}

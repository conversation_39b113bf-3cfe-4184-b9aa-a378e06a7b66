"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { Form } from "@/components/ui/form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import Link from "next/link";
import { ArrowLeft } from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import InputField from "@/components/InputField";
import SecondaryButton from "@/components/SecondaryButton";
import GoogleIcon from "@/components/icons/GoogleIcon";

const loginSchema = z.object({
  email: z.string().email({ message: "Please enter a valid email address" }),
  password: z.string().min(6, "Password must be at least 6 characters"),
});

type LoginFormData = z.infer<typeof loginSchema>;
export default function SignInPage() {
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<LoginFormData>({
    resolver: zod<PERSON><PERSON><PERSON>ver(loginSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  });

  async function onSubmit(values: LoginFormData) {
    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));
      console.log("Login data:", values);
      // Handle successful login here
    } catch (error) {
      console.error("Login error:", error);
    } finally {
      setIsLoading(false);
    }
  }
  return (
    <div className="min-h-screen flex items-center justify-center px-4 py-12">
      <div className="w-full max-w-md">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-white mb-2">Welcome back</h1>
          <p className="text-slate-400">
            Sign in to your account to continue building your portfolio
          </p>
        </div>

        {/* Login Form */}
        <div className="bg-slate-900/50 border border-slate-800 rounded-xl p-8">
          <Form {...form}>
            <form
              onSubmit={form.handleSubmit(onSubmit)}
              className="space-y-6 flex flex-col"
            >
              <InputField
                control={form.control}
                name="email"
                label="Email address"
                placeholder="Enter your email"
                type="email"
              />
              <InputField
                control={form.control}
                name="password"
                label="Password"
                placeholder="Enter your password"
                type="password"
              />
              {/* Submit Button */}
              <SecondaryButton
                type="submit"
                disabled={isLoading}
                text="Sign In"
                className="mt-2"
              />
            </form>
          </Form>
          <div className="flex justify-end items-center w-full my-2">
            <button className="text-pink-400 hover:text-pink-300 transition-colors cursor-pointer text-sm ">
              Forgot password?
            </button>
          </div>

          {/* Divider */}
          <div className="flex items-center w-full gap-2 my-7">
            <div className="flex-1 h-px bg-slate-700" />
            <p className="text-sm text-slate-400">Or continue with</p>
            <div className="flex-1 h-px bg-slate-700" />
          </div>

          {/* Social Login Buttons */}
          <div className="space-y-3">
            <Button
              variant="outline"
              className="w-full h-11 cursor-pointer border-slate-700 text-slate-300 hover:bg-slate-800 hover:text-white transition-colors"
            >
              <GoogleIcon className="text-pink-400 size-5" />
              <p className="text-pink-400">Continue with Google</p>
            </Button>
          </div>
        </div>

        {/* Sign Up Link */}
        <div className="text-center mt-6">
          <p className="text-slate-400">
            Don&apos;t have an account?{" "}
            <Link
              href="/sign-up"
              className="text-pink-400 hover:text-pink-300 transition-colors font-medium"
            >
              Sign up
            </Link>
          </p>
        </div>
        <div className="text-center mt-2">
          <Link
            href="/"
            className="text-pink-400 hover:text-pink-300 transition-colors font-medium flex gap-2 justify-center items-center"
          >
            <ArrowLeft className="size-5" />
            <p className="text-sm">Back to home</p>
          </Link>
        </div>
      </div>
    </div>
  );
}

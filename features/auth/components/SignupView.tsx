"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { Form } from "@/components/ui/form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import Link from "next/link";
import { ArrowLeft } from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import InputField from "@/components/InputField";
import SecondaryButton from "@/components/SecondaryButton";
import GoogleIcon from "@/components/icons/GoogleIcon";

const signupSchema = z.object({
  fullName: z.string().min(2, "Full name must be at least 2 characters"),
  email: z.string().email("Please enter a valid email address"),
  password: z.string().min(6, "Password must be at least 6 characters"),
});

type SignUpFormData = z.infer<typeof signupSchema>;

export default function SignUpPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [agreeToTerms, setAgreeToTerms] = useState(false);

  const form = useForm<SignUpFormData>({
    resolver: zodResolver(signupSchema),
    defaultValues: {
      fullName: "",
      email: "",
      password: "",
    },
  });

  async function onSubmit(values: SignUpFormData) {
    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));
      console.log("Login data:", values);
      // Handle successful login here
    } catch (error) {
      console.error("Login error:", error);
    } finally {
      setIsLoading(false);
    }
  }
  return (
    <div className="min-h-screen flex items-center justify-center px-4 py-12">
      <div className="w-full max-w-md">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-white mb-2">
            Create your account
          </h1>
          <p className="text-slate-400">
            Start building your professional portfolio today
          </p>
        </div>

        {/* Login Form */}
        <div className="bg-slate-900/50 border border-slate-800 rounded-xl p-8">
          <Form {...form}>
            <form
              onSubmit={form.handleSubmit(onSubmit)}
              className="space-y-6 flex flex-col"
            >
              <InputField
                control={form.control}
                name="fullName"
                label="Full name"
                placeholder="Enter your full name"
                type="text"
              />
              <InputField
                control={form.control}
                name="email"
                label="Email address"
                placeholder="Enter your email"
                type="email"
              />
              <InputField
                control={form.control}
                name="password"
                label="Password"
                placeholder="Enter your password"
                type="password"
              />
              {/* Submit Button */}
              <SecondaryButton
                type="submit"
                disabled={isLoading}
                text="Sign Up"
                className="mt-2"
              />
            </form>
          </Form>

          {/* Divider */}
          <div className="flex items-center w-full gap-2 my-7">
            <div className="flex-1 h-px bg-slate-700" />
            <p className="text-sm text-slate-400">Or continue with</p>
            <div className="flex-1 h-px bg-slate-700" />
          </div>

          {/* Social Login Buttons */}
          <div className="space-y-3">
            <Button
              variant="outline"
              className="w-full h-11 cursor-pointer border-slate-700 text-slate-300 hover:bg-slate-800 hover:text-white transition-colors"
            >
              <GoogleIcon className="text-pink-400 size-5" />
              <p className="text-pink-400">Continue with Google</p>
            </Button>
          </div>
        </div>

        {/* Sign In Link */}
        <div className="text-center mt-6">
          <p className="text-slate-400">
            Already have an account?{" "}
            <Link
              href="/login"
              className="text-pink-400 hover:text-pink-300 transition-colors font-medium"
            >
              Sign In
            </Link>
          </p>
        </div>
        <div className="text-center mt-2">
          <Link
            href="/"
            className="text-pink-400 hover:text-pink-300 transition-colors font-medium flex gap-2 justify-center items-center"
          >
            <ArrowLeft className="size-5" />
            <p className="text-sm">Back to home</p>
          </Link>
        </div>
      </div>
    </div>
  );
}

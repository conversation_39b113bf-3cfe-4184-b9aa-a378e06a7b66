@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-primary: #ffffff;
  --color-foreground: #02040e;
  --color-purple: #898ac4;
  --color-pink: #fda5d5;
  --font-inter: var(--font-inter);
}

@layer base {
  * {
    @apply p-0 m-0;
  }
  body {
    @apply text-primary;
  }
}

/* Custom animations */
@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-loop {
  animation: fade-in-up 0.6s ease-out;
}

import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import Footer from "@/components/layout/Footer";

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "MyTag | Launch your profile card",
  description: "Create your profile card in minutes with MyTag.",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning={true}>
      <body className={`${inter.variable} antialiased font-inter`}>
        <div className="min-h-screen w-full bg-[#020617] relative">
          {/* Background Pattern */}
          <div
            className="absolute inset-0 z-0"
            style={{
              background: "#020617",
              backgroundImage: `
        linear-gradient(to right, rgba(71,85,105,0.15) 1px, transparent 1px),
        linear-gradient(to bottom, rgba(71,85,105,0.15) 1px, transparent 1px),
        radial-gradient(circle at 50% 60%, rgba(236,72,153,0.15) 0%, rgba(168,85,247,0.05) 40%, transparent 70%)
      `,
              backgroundSize: "40px 40px, 40px 40px, 100% 100%",
            }}
          />

          {/* Content */}
          <main className="relative z-10 h-screen w-full">
            {children}
            <Footer />
          </main>
        </div>
      </body>
    </html>
  );
}

import { Metadata } from "next";

import PrimaryButton from "@/components/PrimaryButton";
import { ArrowRight } from "lucide-react";

export const metadata: Metadata = {
  title: "404 - Page Not Found | ME Portfolio Builder",
  description:
    "The page you&apos;re looking for doesn&apos;t exist. Get back to building your amazing portfolio!",
};

export default function NotFoundPage() {
  return (
    <div className="relative z-10 min-h-screen flex items-center justify-center px-4">
      <div className="max-w-2xl mx-auto text-center">
        {/* 404 Animation */}
        <div className="mb-8">
          <div className="relative">
            <h1 className="text-9xl font-bold bg-gradient-to-r from-pink-500 to-purple-600 bg-clip-text text-transparent animate-pulse">
              404
            </h1>
            <div className="absolute inset-0 bg-gradient-to-r from-pink-500/20 to-purple-600/20 blur-3xl rounded-full animate-pulse"></div>
          </div>
        </div>

        {/* Error Message */}
        <div className="mb-8">
          <h2 className="text-3xl font-bold text-white mb-4">
            Oops! Page Not Found
          </h2>
          <p className="text-xl text-slate-400 mb-6">
            The page you&apos;re looking for doesn&apos;t exist or has been
            moved. Don&apos;t worry, let&apos;s get you back on track to
            building your amazing portfolio!
          </p>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
          <PrimaryButton
            link="/"
            text="Go Home"
            className="w-52"
            icon={<ArrowRight className="size-5" />}
          />
        </div>

        {/* Floating Elements Animation */}
        <div className="absolute inset-0 pointer-events-none overflow-hidden">
          <div
            className="absolute top-20 left-10 w-2 h-2 bg-pink-500 rounded-full animate-bounce"
            style={{ animationDelay: "0s" }}
          ></div>
          <div
            className="absolute top-40 right-20 w-3 h-3 bg-purple-500 rounded-full animate-bounce"
            style={{ animationDelay: "0.5s" }}
          ></div>
          <div
            className="absolute bottom-40 left-20 w-2 h-2 bg-pink-400 rounded-full animate-bounce"
            style={{ animationDelay: "1s" }}
          ></div>
          <div
            className="absolute bottom-20 right-10 w-3 h-3 bg-purple-400 rounded-full animate-bounce"
            style={{ animationDelay: "1.5s" }}
          ></div>
        </div>
      </div>
    </div>
  );
}

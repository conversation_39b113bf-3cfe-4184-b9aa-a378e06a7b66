"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Form } from "@/components/ui/form";
import { Checkbox } from "@/components/ui/checkbox";
import InputField from "@/components/InputField";
import TextareaField from "@/components/TextareaField";
import SecondaryButton from "@/components/SecondaryButton";
import ProfileCardPreview, {
  ProfileData,
} from "@/components/ProfileCardPreview";

const profileSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  title: z.string().min(2, "Title must be at least 2 characters"),
  description: z.string().min(10, "Description must be at least 10 characters"),
  company: z.string().optional(),
  email: z.string().email("Please enter a valid email address"),
  profileImage: z.string().optional(),
  githubUrl: z.string().optional(),
  linkedinUrl: z.string().optional(),
  twitterUrl: z.string().optional(),
  isAvailable: z.boolean(),
});

type ProfileFormData = z.infer<typeof profileSchema>;

export default function ProfileCardBuilder() {
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<ProfileFormData>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      name: "",
      title: "",
      description: "",
      company: "",
      email: "",
      profileImage: "",
      githubUrl: "",
      linkedinUrl: "",
      twitterUrl: "",
      isAvailable: true,
    },
  });

  const watchedValues = form.watch();

  const profileData: ProfileData = {
    name: watchedValues.name || "",
    title: watchedValues.title || "",
    description: watchedValues.description || "",
    company: watchedValues.company || "",
    email: watchedValues.email || "",
    profileImage: watchedValues.profileImage || "",
    githubUrl: watchedValues.githubUrl || "",
    linkedinUrl: watchedValues.linkedinUrl || "",
    twitterUrl: watchedValues.twitterUrl || "",
    isAvailable: watchedValues.isAvailable ?? true,
  };

  async function onSubmit(values: ProfileFormData) {
    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 2000));
      console.log("Profile data:", values);
      // Handle successful submission here
      alert("Profile card created successfully!");
    } catch (error) {
      console.error("Submission error:", error);
      alert("Failed to create profile card. Please try again.");
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-950 via-slate-900 to-slate-950 relative overflow-hidden">
      {/* Background decoration */}
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(236,72,153,0.1),transparent_50%)]" />
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_80%_20%,rgba(168,85,247,0.1),transparent_50%)]" />

      <div className="relative z-10 py-12 px-4">
        <div className="max-w-7xl mx-auto">
          {/* Header Section */}
          <div className="text-center ">
            <div className="inline-flex items-center gap-2 bg-gradient-to-r from-pink-500/20 to-purple-600/20 border border-pink-500/30 rounded-full px-4 py-2 mb-6">
              <div className="w-2 h-2 bg-pink-500 rounded-full animate-pulse" />
              <span className="text-sm font-medium text-pink-300">
                Profile Builder
              </span>
            </div>
          </div>

          {/* Two Column Layout */}
          <div className="grid grid-cols-1  gap-8 max-w-7xl mx-auto">
            {/* Column 1: Form Section */}
            <div className="order-1">
              <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6 lg:p-8 shadow-2xl h-fit">
                <div className="flex items-center gap-3 mb-6">
                  <div className="w-10 h-10 bg-gradient-to-r from-pink-500 to-purple-600 rounded-xl flex items-center justify-center">
                    <span className="text-white font-bold text-lg">📝</span>
                  </div>
                  <div>
                    <h2 className="text-xl lg:text-2xl font-bold text-white">
                      Profile Information
                    </h2>
                    <p className="text-slate-400 text-sm">
                      Fill out your details below
                    </p>
                  </div>
                </div>
                <Form {...form}>
                  <form
                    onSubmit={form.handleSubmit(onSubmit)}
                    className="space-y-6"
                  >
                    {/* Basic Information Section */}
                    <div className="space-y-4">
                      <div className="flex items-center gap-2 mb-3">
                        <div className="w-1 h-5 bg-gradient-to-b from-pink-500 to-purple-600 rounded-full" />
                        <h3 className="text-base font-semibold text-white">
                          Basic Information
                        </h3>
                      </div>

                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 place-content-baseline w-full">
                        <InputField
                          control={form.control}
                          name="name"
                          label="Full Name"
                          placeholder="Enter your full name"
                          type="text"
                        />

                        <InputField
                          control={form.control}
                          name="title"
                          label="Professional Title"
                          placeholder="e.g., Frontend Developer, Designer"
                          type="text"
                        />

                        <InputField
                          control={form.control}
                          name="email"
                          label="Email Address"
                          placeholder="<EMAIL>"
                          type="email"
                        />
                        <TextareaField
                          control={form.control}
                          name="description"
                          label="About You"
                          placeholder="Tell people about yourself, your skills, and what you're passionate about..."
                          rows={3}
                        />
                      </div>
                    </div>

                    {/* Visual & Social Section */}
                    <div className="space-y-4">
                      <div className="flex items-center gap-2 mb-3">
                        <div className="w-1 h-5 bg-gradient-to-b from-pink-500 to-purple-600 rounded-full" />
                        <h3 className="text-base font-semibold text-white">
                          Visual & Social
                        </h3>
                      </div>

                      <div className="space-y-4">
                        <InputField
                          control={form.control}
                          name="profileImage"
                          label="Profile Image URL (Optional)"
                          placeholder="https://example.com/your-photo.jpg"
                          type="url"
                        />

                        <div className="space-y-3">
                          <InputField
                            control={form.control}
                            name="githubUrl"
                            label="GitHub URL (Optional)"
                            placeholder="https://github.com/username"
                            type="url"
                          />

                          <InputField
                            control={form.control}
                            name="linkedinUrl"
                            label="LinkedIn URL (Optional)"
                            placeholder="https://linkedin.com/in/username"
                            type="url"
                          />

                          <InputField
                            control={form.control}
                            name="twitterUrl"
                            label="Twitter URL (Optional)"
                            placeholder="https://twitter.com/username"
                            type="url"
                          />
                        </div>
                      </div>
                    </div>

                    {/* Availability Section */}
                    <div className="bg-gradient-to-r from-slate-800/30 to-slate-700/30 border border-slate-600/30 rounded-xl p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="text-white font-medium text-sm mb-1">
                            Work Availability
                          </h4>
                          <p className="text-slate-400 text-xs">
                            Show if you're open to opportunities
                          </p>
                        </div>
                        <Checkbox
                          id="isAvailable"
                          checked={form.watch("isAvailable")}
                          onCheckedChange={(checked) =>
                            form.setValue("isAvailable", !!checked)
                          }
                          className="data-[state=checked]:bg-gradient-to-r data-[state=checked]:from-pink-500 data-[state=checked]:to-purple-600 data-[state=checked]:border-pink-500 w-5 h-5"
                        />
                      </div>
                    </div>

                    <div className="pt-4">
                      <SecondaryButton
                        type="submit"
                        disabled={isLoading}
                        isLoading={isLoading}
                        text={
                          isLoading
                            ? "Creating Your Profile..."
                            : "Create Profile Card"
                        }
                        className="w-full h-11 text-base font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
                      />
                    </div>
                  </form>
                </Form>
              </div>
            </div>

            {/* Column 2: Preview Section */}
            <div className="order-2">
              <div className="sticky top-8">
                <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6 shadow-2xl">
                  <div className="flex items-center gap-3 mb-6">
                    <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-emerald-600 rounded-xl flex items-center justify-center">
                      <span className="text-white font-bold text-lg">👁️</span>
                    </div>
                    <div>
                      <h2 className="text-xl font-bold text-white">
                        Live Preview
                      </h2>
                      <p className="text-slate-400 text-sm">
                        See your changes in real-time
                      </p>
                    </div>
                  </div>

                  <div className="bg-gradient-to-br from-slate-800/30 to-slate-900/30 border border-slate-700/50 rounded-xl p-2 lg:p-4 overflow-hidden">
                    <div className="transform scale-[0.65] sm:scale-75 lg:scale-90 origin-top-left">
                      <ProfileCardPreview data={profileData} />
                    </div>
                  </div>

                  <div className="mt-4 p-3 bg-gradient-to-r from-blue-500/10 to-purple-500/10 border border-blue-500/20 rounded-lg">
                    <p className="text-blue-300 text-xs text-center">
                      ✨ Your profile updates automatically as you type
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

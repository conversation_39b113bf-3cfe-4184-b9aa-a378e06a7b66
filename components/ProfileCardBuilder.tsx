"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Form } from "@/components/ui/form";
import { Checkbox } from "@/components/ui/checkbox";
import InputField from "@/components/InputField";
import TextareaField from "@/components/TextareaField";
import SecondaryButton from "@/components/SecondaryButton";
import ProfileCardPreview, { ProfileData } from "@/components/ProfileCardPreview";
import { Eye, EyeOff } from "lucide-react";

const profileSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  title: z.string().min(2, "Title must be at least 2 characters"),
  description: z.string().min(10, "Description must be at least 10 characters"),
  company: z.string().optional(),
  email: z.string().email("Please enter a valid email address"),
  profileImage: z.string().url("Please enter a valid image URL").optional().or(z.literal("")),
  githubUrl: z.string().url("Please enter a valid GitHub URL").optional().or(z.literal("")),
  linkedinUrl: z.string().url("Please enter a valid LinkedIn URL").optional().or(z.literal("")),
  twitterUrl: z.string().url("Please enter a valid Twitter URL").optional().or(z.literal("")),
  isAvailable: z.boolean().default(true),
});

type ProfileFormData = z.infer<typeof profileSchema>;

export default function ProfileCardBuilder() {
  const [isLoading, setIsLoading] = useState(false);
  const [showPreview, setShowPreview] = useState(true);

  const form = useForm<ProfileFormData>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      name: "",
      title: "",
      description: "",
      company: "",
      email: "",
      profileImage: "",
      githubUrl: "",
      linkedinUrl: "",
      twitterUrl: "",
      isAvailable: true,
    },
  });

  const watchedValues = form.watch();

  const profileData: ProfileData = {
    name: watchedValues.name || "",
    title: watchedValues.title || "",
    description: watchedValues.description || "",
    company: watchedValues.company || "",
    email: watchedValues.email || "",
    profileImage: watchedValues.profileImage || "",
    githubUrl: watchedValues.githubUrl || "",
    linkedinUrl: watchedValues.linkedinUrl || "",
    twitterUrl: watchedValues.twitterUrl || "",
    isAvailable: watchedValues.isAvailable ?? true,
  };

  async function onSubmit(values: ProfileFormData) {
    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 2000));
      console.log("Profile data:", values);
      // Handle successful submission here
      alert("Profile card created successfully!");
    } catch (error) {
      console.error("Submission error:", error);
      alert("Failed to create profile card. Please try again.");
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <div className="min-h-screen bg-slate-950 py-8 px-4">
      <div className="max-w-7xl mx-auto">
        <div className="mb-8 text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">
            <span className="bg-gradient-to-r from-white to-slate-300 bg-clip-text text-transparent">
              Build Your
            </span>
            <br />
            <span className="bg-gradient-to-r from-pink-500 to-purple-600 bg-clip-text text-transparent">
              Profile Card
            </span>
          </h1>
          <p className="text-xl text-slate-400 max-w-2xl mx-auto">
            Fill out the form below and see your profile card come to life in real-time
          </p>
        </div>

        {/* Toggle Preview Button */}
        <div className="flex justify-center mb-8">
          <button
            onClick={() => setShowPreview(!showPreview)}
            className="flex items-center gap-2 px-4 py-2 bg-slate-800 hover:bg-slate-700 text-white rounded-lg transition-colors"
          >
            {showPreview ? <EyeOff size={20} /> : <Eye size={20} />}
            {showPreview ? "Hide Preview" : "Show Preview"}
          </button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Form Section */}
          <div className="bg-slate-900/50 border border-slate-800 rounded-xl p-8">
            <h2 className="text-2xl font-semibold text-white mb-6">Profile Information</h2>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                <InputField
                  control={form.control}
                  name="name"
                  label="Full Name"
                  placeholder="Enter your full name"
                  type="text"
                />

                <InputField
                  control={form.control}
                  name="title"
                  label="Professional Title"
                  placeholder="e.g., Frontend Developer, Designer"
                  type="text"
                />

                <TextareaField
                  control={form.control}
                  name="description"
                  label="Description"
                  placeholder="Tell people about yourself and what you do..."
                  rows={4}
                />

                <InputField
                  control={form.control}
                  name="company"
                  label="Company (Optional)"
                  placeholder="Your current company"
                  type="text"
                />

                <InputField
                  control={form.control}
                  name="email"
                  label="Email Address"
                  placeholder="<EMAIL>"
                  type="email"
                />

                <InputField
                  control={form.control}
                  name="profileImage"
                  label="Profile Image URL (Optional)"
                  placeholder="https://example.com/your-photo.jpg"
                  type="url"
                />

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <InputField
                    control={form.control}
                    name="githubUrl"
                    label="GitHub URL (Optional)"
                    placeholder="https://github.com/username"
                    type="url"
                  />

                  <InputField
                    control={form.control}
                    name="linkedinUrl"
                    label="LinkedIn URL (Optional)"
                    placeholder="https://linkedin.com/in/username"
                    type="url"
                  />

                  <InputField
                    control={form.control}
                    name="twitterUrl"
                    label="Twitter URL (Optional)"
                    placeholder="https://twitter.com/username"
                    type="url"
                  />
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="isAvailable"
                    checked={form.watch("isAvailable")}
                    onCheckedChange={(checked) => form.setValue("isAvailable", !!checked)}
                    className="data-[state=checked]:bg-pink-500 data-[state=checked]:border-pink-500"
                  />
                  <label
                    htmlFor="isAvailable"
                    className="text-sm font-medium text-slate-300 cursor-pointer"
                  >
                    Available for work
                  </label>
                </div>

                <SecondaryButton
                  type="submit"
                  disabled={isLoading}
                  isLoading={isLoading}
                  text={isLoading ? "Creating..." : "Create Profile Card"}
                  className="w-full mt-8"
                />
              </form>
            </Form>
          </div>

          {/* Preview Section */}
          {showPreview && (
            <div className="lg:sticky lg:top-8 lg:h-fit">
              <h2 className="text-2xl font-semibold text-white mb-6 text-center">Live Preview</h2>
              <div className="bg-slate-900/30 border border-slate-800 rounded-xl p-6">
                <ProfileCardPreview data={profileData} />
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

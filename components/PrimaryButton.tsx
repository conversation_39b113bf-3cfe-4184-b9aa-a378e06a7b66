import { cn } from "@/lib/utils";
import Link from "next/link";

interface PrimaryButtonProps {
  className?: string;
  icon?: React.ReactNode;
  text: string;
  link: string;
  onClick?: () => void;
}

export default function PrimaryButton({
  className,
  icon,
  text,
  onClick,
  link,
}: PrimaryButtonProps) {
  return (
    <Link
      href={link}
      onClick={onClick}
      className={cn(
        "bg-gradient-to-r flex justify-center items-center gap-1 px-7 rounded-lg h-11 cursor-pointer from-pink-500 to-purple-600 hover:from-pink-600 hover:to-purple-700 text-white",
        className
      )}
    >
      <p className="font-medium">{text}</p>
      {icon && <span>{icon}</span>}
    </Link>
  );
}

"use client";
import { usePathname } from "next/navigation";
import Link from "next/link";

import PrimaryButton from "../PrimaryButton";

export default function Navbar() {
  const pathName = usePathname();
  const shouldHideNav = ["/login", "/sign-up", "/not-found"];
  if (shouldHideNav.includes(pathName)) {
    return null;
  }
  return (
    <nav className="h-16 w-full border-b border-slate-800/50 bg-slate-950/50 backdrop-blur-sm">
      <div className="max-w-7xl mx-auto h-full flex justify-between items-center px-4">
        {/* Logo */}
        <Link href="/" className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-gradient-to-br from-pink-500 to-purple-600 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-sm">ME</span>
          </div>
        </Link>

        {/* Auth Buttons */}
        <div className="flex items-center space-x-3">
          <PrimaryButton text="Get Started" link="/sign-up" className="h-10" />
        </div>
      </div>
    </nav>
  );
}

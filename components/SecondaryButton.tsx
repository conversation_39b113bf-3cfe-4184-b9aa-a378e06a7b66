import { cn } from "@/lib/utils";
import { But<PERSON> } from "./ui/button";
import MiniSpinner from "./MiniSpinner";

interface SecondaryButtonProps {
  className?: string;
  type: "button" | "submit" | "reset";
  icon?: React.ReactNode;
  text: string;
  onClick?: () => void;
  disabled?: boolean;
  isLoading?: boolean;
}

export default function SecondaryButton({
  className,
  icon,
  text,
  onClick,
  disabled,
  isLoading,
  type,
}: SecondaryButtonProps) {
  return (
    <Button
      disabled={disabled}
      onClick={onClick}
      type={type}
      className={cn(
        "bg-gradient-to-r h-11 rounded-lg cursor-pointer from-pink-500 to-purple-600 hover:from-pink-600 hover:to-purple-700 text-white flex justify-center items-center gap-1",
        className
      )}
    >
      {isLoading ? (
        <MiniSpinner />
      ) : (
        <>
          {icon && <span>{icon}</span>}
          <p className="text-lg font-semibold">{text}</p>
        </>
      )}
    </Button>
  );
}
